# AI智能任务系统记忆功能 - 技术方案 - 其他功能

## 文档说明
本文档包含除了模块结构优化和记忆工具开发之外的所有其他技术实现内容，包括AI系统集成、前端界面开发、测试和部署等。

**前置条件：** 
1. 必须先完成模块结构优化
2. 必须先完成记忆工具开发

**依赖关系：** 依赖于前两个技术方案的完成，在此基础上进行系统集成和功能完善。

## AI工具注册和集成

### AI系统集成 (ai/index.obj.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/index.obj.js 修改部分

// 导入记忆工具
const MemoryTool = require('./modules/memory')

module.exports = {
  // 现有代码...

  /**
   * 直接调用工具（扩展支持记忆工具）
   */
  async callToolDirect({ toolName, parameters = {}, token = null }) {
    if (!toolName) {
      return { errCode: 'PARAM_IS_NULL', errMsg: 'toolName 不能为空' }
    }

    try {
      console.log('[callToolDirect] start', { toolName, parameters })

      // 记忆工具路由
      if (toolName.startsWith('memory_') || ['createMemory', 'getMemories', 'updateMemory', 'deleteMemory'].includes(toolName)) {
        const memoryTool = new MemoryTool()
        const method = toolName.startsWith('memory_') ? toolName.replace('memory_', '') : toolName

        // 自动添加用户ID
        if (!parameters.userId) {
          const token = this.getUniIdToken()
          if (token && token.uid) {
            parameters.userId = token.uid
          } else {
            return { errCode: 'UNAUTHORIZED', errMsg: '用户未登录' }
          }
        }

        const res = await memoryTool.execute(method, parameters)
        console.log('[callToolDirect] memory tool result:', JSON.stringify(res).slice(0, 1200))
        return res
      }

      // 原有的todo工具处理
      const todoTool = new TodoTool()
      // ... 原有逻辑

    } catch (e) {
      console.error('[callToolDirect] error', e && e.message)
      return { errCode: 'SYSTEM_ERROR', errMsg: e?.message || 'callToolDirect 失败', data: null }
    }
  }
}
```

### chatStreamSSE集成记忆功能

```javascript
// uniCloud-aliyun/cloudfunctions/ai/index.obj.js 中的 chatStreamSSE 修改

async chatStreamSSE({ message, messages, channel }) {
  const userId = this.getUniIdToken()?.uid

  try {
    // 导入记忆工具函数
    const { isMemoryCommand, extractMemoryContent, buildSystemPromptWithMemories } =
      require('./modules/memory/utils')
    const MemoryTool = require('./modules/memory')

    // 1. 检查是否是记忆指令
    if (isMemoryCommand(message)) {
      const memoryContent = extractMemoryContent(message)
      const memoryTool = new MemoryTool()

      // 创建记忆
      const result = await memoryTool.execute('createMemory', {
        userId,
        content: memoryContent
      })

      if (result.errCode) {
        channel.write({
          type: 'message',
          data: { content: `记忆保存失败：${result.errMsg}` }
        })
      } else {
        channel.write({
          type: 'message',
          data: { content: '好的，我已经记住了这个信息。' }
        })
      }

      return { errCode: 0, errMsg: 'success' }
    }

    // 2. 加载用户记忆并构建系统提示词
    let systemPrompt = '你是一个AI助手，帮助用户管理任务。'

    if (userId) {
      const memoryTool = new MemoryTool()
      const memoriesResult = await memoryTool.execute('getMemories', {
        userId,
        limit: 10
      })

      if (!memoriesResult.errCode && memoriesResult.data) {
        systemPrompt = buildSystemPromptWithMemories(memoriesResult.data)
      }
    }

    // 3. 使用包含记忆的系统提示词进行AI对话
    // ... 原有的AI对话逻辑，使用修改后的systemPrompt

  } catch (error) {
    console.error('记忆功能错误:', error)
    // 继续正常的AI对话流程
  }
}
```

## 工具调用示例

### 记忆工具调用方式

```javascript
// 可以通过以下方式调用记忆工具：

// 1. 直接工具调用
await this.callToolDirect({
  toolName: 'createMemory',
  parameters: {
    content: '用户的记忆内容',
    startDate: '2024-01-01',
    endDate: '2024-12-31'
  }
})

// 2. 批量操作
await this.callToolDirect({
  toolName: 'getMemories',
  parameters: {
    limit: 20,
    offset: 0
  }
})

// 3. 记忆管理
await this.callToolDirect({
  toolName: 'updateMemory',
  parameters: {
    memoryId: 'memory_123',
    content: '更新后的记忆内容'
  }
})

await this.callToolDirect({
  toolName: 'deleteMemory',
  parameters: {
    memoryId: 'memory_123'
  }
})
```

## 前端界面开发

### API调用封装

```javascript
// src/api/memory.js
import { request } from '@/utils/request'

/**
 * 创建记忆
 */
export function createMemory(data) {
  return request({
    url: '/ai/callToolDirect',
    method: 'POST',
    data: {
      toolName: 'createMemory',
      parameters: data
    }
  })
}

/**
 * 获取记忆列表
 */
export function getMemories(params = {}) {
  return request({
    url: '/ai/callToolDirect',
    method: 'POST',
    data: {
      toolName: 'getMemories',
      parameters: params
    }
  })
}

/**
 * 更新记忆
 */
export function updateMemory(data) {
  return request({
    url: '/ai/callToolDirect',
    method: 'POST',
    data: {
      toolName: 'updateMemory',
      parameters: data
    }
  })
}

/**
 * 删除记忆
 */
export function deleteMemory(memoryId) {
  return request({
    url: '/ai/callToolDirect',
    method: 'POST',
    data: {
      toolName: 'deleteMemory',
      parameters: { memoryId }
    }
  })
}
```

### 记忆管理界面组件

```vue
<!-- src/components/z-memory-manager/z-memory-manager.vue -->
<template>
  <view class="memory-manager">
    <view class="header">
      <text class="title">我的记忆</text>
      <view class="add-btn" @click="showAddDialog = true">
        <i class="fas fa-plus"></i>
        <text>添加记忆</text>
      </view>
    </view>

    <view class="memory-list">
      <view 
        v-for="memory in memories" 
        :key="memory._id"
        class="memory-item"
      >
        <view class="content">{{ memory.content }}</view>
        <view class="meta">
          <text class="time">{{ formatTime(memory.createTime) }}</text>
          <view class="actions">
            <view class="action-btn" @click="editMemory(memory)">
              <i class="fas fa-edit"></i>
            </view>
            <view class="action-btn delete" @click="confirmDelete(memory)">
              <i class="fas fa-trash"></i>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加/编辑记忆弹窗 -->
    <uni-popup ref="memoryDialog" type="dialog">
      <uni-popup-dialog 
        :title="editingMemory ? '编辑记忆' : '添加记忆'"
        :content="dialogContent"
        @confirm="saveMemory"
        @close="closeDialog"
      />
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getMemories, createMemory, updateMemory, deleteMemory } from '@/api/memory'

const memories = ref([])
const showAddDialog = ref(false)
const editingMemory = ref(null)
const dialogContent = ref('')

// 加载记忆列表
const loadMemories = async () => {
  try {
    const result = await getMemories()
    if (!result.errCode) {
      memories.value = result.data || []
    }
  } catch (error) {
    uni.showToast({ title: '加载失败', icon: 'none' })
  }
}

// 保存记忆
const saveMemory = async (content) => {
  try {
    if (editingMemory.value) {
      await updateMemory({
        memoryId: editingMemory.value._id,
        content
      })
    } else {
      await createMemory({ content })
    }
    
    uni.showToast({ title: '保存成功' })
    loadMemories()
    closeDialog()
  } catch (error) {
    uni.showToast({ title: '保存失败', icon: 'none' })
  }
}

// 删除记忆
const confirmDelete = (memory) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条记忆吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await deleteMemory(memory._id)
          uni.showToast({ title: '删除成功' })
          loadMemories()
        } catch (error) {
          uni.showToast({ title: '删除失败', icon: 'none' })
        }
      }
    }
  })
}

onMounted(() => {
  loadMemories()
})
</script>
```

## 测试和部署

### 功能测试清单

#### 记忆CRUD操作测试
- [ ] 创建记忆功能测试
- [ ] 获取记忆列表功能测试
- [ ] 更新记忆功能测试
- [ ] 删除记忆功能测试
- [ ] 记忆数量限制测试
- [ ] 内容长度限制测试

#### AI对话记忆应用测试
- [ ] 记忆指令识别测试
- [ ] 记忆内容提取测试
- [ ] 系统提示词构建测试
- [ ] 个性化回复测试
- [ ] 记忆加载性能测试

#### 工具间协作测试
- [ ] 记忆工具与todo工具协作测试
- [ ] 基类继承功能测试
- [ ] 错误处理统一性测试
- [ ] 日志记录标准化测试

### 性能优化

#### 数据库优化
```javascript
// 为记忆表创建索引
// uniCloud-aliyun/database/memory.schema.json 添加索引配置
{
  "indexes": [
    {
      "IndexName": "userId_createTime",
      "MgoKeySchema": {
        "MgoIndexKeys": [
          {
            "Name": "userId",
            "Direction": "1"
          },
          {
            "Name": "createTime",
            "Direction": "-1"
          }
        ],
        "MgoIsUnique": false
      }
    }
  ]
}
```

#### 缓存策略
```javascript
// 记忆缓存优化
class MemoryManager {
  constructor() {
    this.db = uniCloud.database()
    this.collection = this.db.collection('memory')
    this.cache = new Map() // 简单内存缓存
  }

  async getMemories(options = {}) {
    const { userId, limit = MEMORY_CONFIG.MEMORY_QUERY_LIMIT } = options
    const cacheKey = `memories_${userId}_${limit}`

    // 检查缓存
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)
      if (Date.now() - cached.timestamp < 60000) { // 1分钟缓存
        return createSuccessResponse(cached.data, '获取记忆列表成功（缓存）')
      }
    }

    // 从数据库查询
    const result = await this.queryFromDatabase(options)

    // 更新缓存
    if (!result.errCode) {
      this.cache.set(cacheKey, {
        data: result.data,
        timestamp: Date.now()
      })
    }

    return result
  }
}
```

## 实施计划

### 第三阶段：测试和优化 (2天)

1. **功能测试**
   - 记忆CRUD操作测试
   - AI对话记忆应用测试
   - 工具间协作测试

2. **前端界面开发**
   - 记忆管理界面
   - API调用封装

3. **性能优化和部署**
   - 查询性能优化
   - 生产环境部署

### 部署验证清单

- [ ] AI系统集成完成
- [ ] chatStreamSSE记忆功能集成完成
- [ ] 前端API封装完成
- [ ] 记忆管理界面开发完成
- [ ] 功能测试全部通过
- [ ] 性能优化完成
- [ ] 数据库索引创建完成
- [ ] 生产环境部署完成
- [ ] 用户验收测试通过
```
