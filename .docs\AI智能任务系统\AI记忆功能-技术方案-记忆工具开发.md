# AI智能任务系统记忆功能 - 技术方案 - 记忆工具开发

## 文档说明
本文档专门描述记忆工具的添加、记忆功能的具体实现等内容。

**前置条件：** 必须先完成模块结构优化，包括共享基础配置、工具函数重构、工具基类设计等。

**依赖关系：** 依赖于模块结构优化的完成，使用共享的基础配置和工具基类。

## 记忆模块架构

### 模块结构
```
uniCloud-aliyun/cloudfunctions/ai/modules/memory/
├── index.js             # 记忆工具主入口，继承BaseTool
├── config.js            # 记忆模块特有配置
├── memories.js          # 记忆管理核心逻辑
└── utils.js             # 记忆特有工具函数
```

## 记忆特有配置 (memory/config.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/config.js
const { COMMON_ERROR_CODES, BASE_CONFIG } = require('../common/base-config')

// 记忆模块特有配置
const MEMORY_CONFIG = {
  // 继承基础配置
  ...BASE_CONFIG,

  // 记忆特有配置
  MAX_MEMORIES_PER_USER: 100,
  MEMORY_QUERY_LIMIT: 50,

  // 记忆识别关键词
  MEMORY_KEYWORDS: [
    '请记住', '记住', '记录一下', '保存这个信息',
    '我要告诉你', '你需要知道', '重要信息'
  ]
}

// 记忆模块特有错误码
const MEMORY_ERROR_CODES = {
  // 继承通用错误码
  ...COMMON_ERROR_CODES,

  // 记忆特有错误码
  MEMORY_NOT_FOUND: 'MEMORY_NOT_FOUND',
  CONTENT_TOO_LONG: 'CONTENT_TOO_LONG',
  MEMORY_LIMIT_EXCEEDED: 'MEMORY_LIMIT_EXCEEDED'
}

module.exports = {
  MEMORY_CONFIG,
  MEMORY_ERROR_CODES
}
```

## 记忆管理核心类 (memory/memories.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/memories.js
const { MEMORY_CONFIG, MEMORY_ERROR_CODES } = require('./config')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  generateId,
  formatDateTime
} = require('../common/base-utils')

/**
 * 记忆管理类
 */
class MemoryManager {
  constructor() {
    this.db = uniCloud.database()
    this.collection = this.db.collection('memory')
  }

  /**
   * 创建记忆
   */
  async createMemory(options = {}) {
    const { userId, content, startDate = null, endDate = null } = options

    // 参数校验
    const validation = validateParams({ userId, content }, ['userId', 'content'])
    if (validation) return validation

    // 内容长度校验
    if (content.length > MEMORY_CONFIG.MAX_CONTENT_LENGTH) {
      return createErrorResponse(
        MEMORY_ERROR_CODES.CONTENT_TOO_LONG,
        `记忆内容不能超过${MEMORY_CONFIG.MAX_CONTENT_LENGTH}个字符`
      )
    }

    try {
      // 检查用户记忆数量限制
      const { total } = await this.collection.where({ userId }).count()
      if (total >= MEMORY_CONFIG.MAX_MEMORIES_PER_USER) {
        return createErrorResponse(
          MEMORY_ERROR_CODES.MEMORY_LIMIT_EXCEEDED,
          `每个用户最多只能保存${MEMORY_CONFIG.MAX_MEMORIES_PER_USER}条记忆`
        )
      }

      const now = formatDateTime()
      const memoryData = {
        _id: generateId(),
        userId,
        content: content.trim(),
        startDate,
        endDate,
        createTime: now,
        updateTime: now
      }

      const result = await this.collection.add(memoryData)

      return createSuccessResponse({
        memoryId: result.id,
        ...memoryData
      }, '记忆创建成功')

    } catch (error) {
      console.error('[MemoryManager.createMemory] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }

  /**
   * 获取记忆列表
   */
  async getMemories(options = {}) {
    const { userId, limit = MEMORY_CONFIG.MEMORY_QUERY_LIMIT, offset = 0 } = options

    // 参数校验
    const validation = validateParams({ userId }, ['userId'])
    if (validation) return validation

    try {
      const query = this.collection
        .where({ userId })
        .orderBy('createTime', 'desc')
        .skip(offset)
        .limit(Math.min(limit, MEMORY_CONFIG.MAX_QUERY_LIMIT))

      const result = await query.get()

      return createSuccessResponse(result.data, '获取记忆列表成功')

    } catch (error) {
      console.error('[MemoryManager.getMemories] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }

  /**
   * 更新记忆
   */
  async updateMemory(options = {}) {
    const { userId, memoryId, content, startDate, endDate } = options

    // 参数校验
    const validation = validateParams({ userId, memoryId }, ['userId', 'memoryId'])
    if (validation) return validation

    try {
      const updateData = {
        updateTime: formatDateTime()
      }

      if (content !== undefined) {
        if (content.length > MEMORY_CONFIG.MAX_CONTENT_LENGTH) {
          return createErrorResponse(
            MEMORY_ERROR_CODES.CONTENT_TOO_LONG,
            `记忆内容不能超过${MEMORY_CONFIG.MAX_CONTENT_LENGTH}个字符`
          )
        }
        updateData.content = content.trim()
      }

      if (startDate !== undefined) updateData.startDate = startDate
      if (endDate !== undefined) updateData.endDate = endDate

      const result = await this.collection
        .where({ _id: memoryId, userId })
        .update(updateData)

      if (result.updated === 0) {
        return createErrorResponse(MEMORY_ERROR_CODES.MEMORY_NOT_FOUND, '记忆不存在或无权限访问')
      }

      return createSuccessResponse({ memoryId, ...updateData }, '记忆更新成功')

    } catch (error) {
      console.error('[MemoryManager.updateMemory] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }

  /**
   * 删除记忆
   */
  async deleteMemory(options = {}) {
    const { userId, memoryId } = options

    // 参数校验
    const validation = validateParams({ userId, memoryId }, ['userId', 'memoryId'])
    if (validation) return validation

    try {
      const result = await this.collection
        .where({ _id: memoryId, userId })
        .remove()

      if (result.deleted === 0) {
        return createErrorResponse(MEMORY_ERROR_CODES.MEMORY_NOT_FOUND, '记忆不存在或无权限访问')
      }

      return createSuccessResponse({ memoryId }, '记忆删除成功')

    } catch (error) {
      console.error('[MemoryManager.deleteMemory] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }
}

module.exports = MemoryManager
```

## 记忆工具主入口 (memory/index.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/index.js
const BaseTool = require('../common/base-tool')
const MemoryManager = require('./memories')

/**
 * 记忆工具类
 * 继承BaseTool，实现记忆相关功能
 */
class MemoryTool extends BaseTool {
  constructor() {
    super('MemoryTool')
    this.memoryManager = new MemoryManager()
  }

  /**
   * 获取可用方法列表
   * @returns {Array} 方法名数组
   */
  getAvailableMethods() {
    return ['createMemory', 'getMemories', 'updateMemory', 'deleteMemory']
  }

  /**
   * 创建记忆
   */
  async createMemory(parameters) {
    return await this.memoryManager.createMemory(parameters)
  }

  /**
   * 获取记忆列表
   */
  async getMemories(parameters) {
    return await this.memoryManager.getMemories(parameters)
  }

  /**
   * 更新记忆
   */
  async updateMemory(parameters) {
    return await this.memoryManager.updateMemory(parameters)
  }

  /**
   * 删除记忆
   */
  async deleteMemory(parameters) {
    return await this.memoryManager.deleteMemory(parameters)
  }
}

module.exports = MemoryTool
```

## 记忆特有工具函数 (memory/utils.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/utils.js
const { MEMORY_CONFIG } = require('./config')

/**
 * 记忆识别逻辑
 */
function isMemoryCommand(message) {
  return MEMORY_CONFIG.MEMORY_KEYWORDS.some(keyword =>
    message.toLowerCase().includes(keyword.toLowerCase())
  )
}

/**
 * 提取记忆内容
 */
function extractMemoryContent(message) {
  const patterns = [
    /请记住[，：:]\s*(.*)/,
    /记住[，：:]\s*(.*)/,
    /记录一下[，：:]\s*(.*)/,
    /保存这个信息[，：:]\s*(.*)/,
    /我要告诉你[，：:]\s*(.*)/,
    /你需要知道[，：:]\s*(.*)/
  ]

  for (const pattern of patterns) {
    const match = message.match(pattern)
    if (match) {
      return match[1].trim()
    }
  }

  return message.trim()
}

/**
 * 构建包含记忆的系统提示词
 */
function buildSystemPromptWithMemories(memories) {
  if (!memories || memories.length === 0) {
    return '你是一个AI助手，帮助用户管理任务。'
  }

  const memoryText = memories
    .map((m, index) => `${index + 1}. ${m.content}`)
    .join('\n')

  return `你是一个AI助手，帮助用户管理任务。

用户之前告诉过你以下信息，请在回答时考虑这些背景：
${memoryText}

请基于这些信息提供个性化的建议和回复。`
}

module.exports = {
  isMemoryCommand,
  extractMemoryContent,
  buildSystemPromptWithMemories
}
```

## 实施计划

### 第二阶段：记忆模块开发 (2天)

1. **数据库Schema创建**
   - 创建 `uniCloud-aliyun/database/memory.schema.json`
   - 配置权限和字段验证规则

2. **记忆工具模块开发**
   - 创建 `modules/memory/` 文件夹结构
   - 实现继承BaseTool的MemoryTool
   - 实现记忆管理核心逻辑

3. **AI系统集成**
   - 在 `ai/index.obj.js` 中集成MemoryTool
   - 实现chatStreamSSE记忆功能集成

### 开发验证清单

- [ ] 记忆数据库Schema创建完成
- [ ] 记忆配置模块实现完成
- [ ] 记忆管理核心类实现完成
- [ ] 记忆工具主入口实现完成
- [ ] 记忆工具函数实现完成
- [ ] 记忆CRUD操作测试通过
- [ ] 工具基类继承验证通过
