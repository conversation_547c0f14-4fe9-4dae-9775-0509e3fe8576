# AI智能任务系统记忆功能需求

## 背景

当前AI智能任务系统已具备基础的任务管理和AI对话能力，但AI无法记住用户之前告诉它的信息，每次对话都是全新开始。通过添加简单的记忆功能，AI可以记住用户主动分享的信息，在后续对话中提供更个性化的建议。

## 需求

### 功能需求

#### 1. 记忆存储功能
- **用户主动输入记忆**：用户可以主动告诉AI需要记住的信息
- **AI识别记忆内容**：AI能够识别用户想要记忆的内容并保存
- **记忆持久化存储**：将记忆内容保存到数据库中

#### 2. 记忆应用功能
- **自动加载记忆**：每次AI对话时自动从数据库加载用户记忆
- **记忆融入对话**：将记忆内容作为上下文信息加入AI提示词
- **个性化回复**：基于记忆内容提供个性化的回复和建议

#### 3. 记忆管理功能
- **记忆查看**：用户可以查看当前存储的所有记忆
- **记忆删除**：用户可以删除不需要的记忆
- **记忆编辑**：用户可以修改已存储的记忆内容

### 非功能需求

#### 1. 性能要求
- 记忆查询响应时间 < 200ms
- 记忆存储操作 < 500ms
- 不影响AI对话的响应速度

#### 2. 数据安全
- 记忆数据安全存储
- 用户隐私保护
- 按用户隔离存储

#### 3. 用户体验
- 记忆功能简单易用
- 记忆应用过程对用户透明
- 提供清晰的记忆管理界面

## 技术方案

### 数据结构设计

#### 记忆数据表 (Memory)
```typescript
interface Memory {
  _id: string
  userId: string // 用户ID
  content: string // 记忆内容（纯文本）
  startDate?: string // 开始日期（YYYY-MM-DD格式）
  endDate?: string // 结束日期（YYYY-MM-DD格式）
  createTime: string // 创建时间（ISO 8601格式）
  updateTime: string // 更新时间（ISO 8601格式）
}
```

#### uniCloud数据库Schema定义
```json
// uniCloud-aliyun/database/memory.schema.json
{
  "bsonType": "object",
  "required": ["userId", "content", "createTime", "updateTime"],
  "permission": {
    "read": "auth.uid == doc.userId",
    "create": "auth.uid != null",
    "update": "auth.uid == doc.userId",
    "delete": "auth.uid == doc.userId"
  },
  "properties": {
    "_id": {
      "description": "记忆ID，系统自动生成"
    },
    "userId": {
      "bsonType": "string",
      "description": "用户ID，关联uni-id-users表"
    },
    "content": {
      "bsonType": "string",
      "description": "记忆内容，纯文本格式",
      "maxLength": 2000
    },
    "startDate": {
      "bsonType": "string",
      "description": "开始日期，YYYY-MM-DD格式，可选",
      "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
    },
    "endDate": {
      "bsonType": "string",
      "description": "结束日期，YYYY-MM-DD格式，可选",
      "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
    },
    "createTime": {
      "bsonType": "string",
      "description": "创建时间，ISO 8601格式"
    },
    "updateTime": {
      "bsonType": "string",
      "description": "更新时间，ISO 8601格式"
    }
  }
}
```

### 实现架构

#### 1. 优化后的模块结构
```
uniCloud-aliyun/cloudfunctions/ai/modules/
├── common/                    # 新增：共享工具模块
│   ├── base-config.js        # 基础配置和错误码
│   ├── base-utils.js         # 通用工具函数
│   └── base-tool.js          # 工具基类
├── todo/                     # 现有todo模块（需要重构）
│   ├── index.js             # 继承BaseTool
│   ├── config.js            # 只保留todo特有配置
│   ├── tasks.js
│   ├── projects.js
│   └── utils.js             # 只保留todo特有工具函数
└── memory/                   # 新增记忆模块
    ├── index.js             # 继承BaseTool
    ├── config.js            # 只保留记忆特有配置
    ├── memories.js          # 记忆管理核心逻辑
    └── utils.js             # 只保留记忆特有工具函数
```

#### 2. 共享基础配置 (common/base-config.js)
```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/common/base-config.js

// 通用错误码定义
const COMMON_ERROR_CODES = {
  // 参数错误
  PARAM_IS_NULL: 'PARAM_IS_NULL',
  PARAM_INVALID: 'PARAM_INVALID',

  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',

  // 数据库错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  DATA_NOT_FOUND: 'DATA_NOT_FOUND',

  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',

  // 业务错误
  OPERATION_FAILED: 'OPERATION_FAILED',
  RESOURCE_LIMIT_EXCEEDED: 'RESOURCE_LIMIT_EXCEEDED',

  // 未知错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

// 通用响应模板
const BASE_SUCCESS_RESPONSE = {
  success: true,
  message: '操作成功',
  data: null,
  // 兼容旧格式
  errCode: null,
  errMsg: '操作成功'
}

const BASE_ERROR_RESPONSE = {
  success: false,
  message: '操作失败',
  data: null,
  // 兼容旧格式
  errCode: 'UNKNOWN_ERROR',
  errMsg: '操作失败',
  details: null
}

// 通用配置
const BASE_CONFIG = {
  // 数据库查询限制
  DEFAULT_QUERY_LIMIT: 50,
  MAX_QUERY_LIMIT: 200,

  // 日期时间格式
  DATE_FORMAT: 'YYYY-MM-DD',
  DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',

  // 通用字段长度限制
  MAX_TITLE_LENGTH: 200,
  MAX_CONTENT_LENGTH: 2000,
  MAX_DESCRIPTION_LENGTH: 500
}

module.exports = {
  COMMON_ERROR_CODES,
  BASE_SUCCESS_RESPONSE,
  BASE_ERROR_RESPONSE,
  BASE_CONFIG
}
```

#### 3. 共享工具函数 (common/base-utils.js)
```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/common/base-utils.js
const {
  COMMON_ERROR_CODES,
  BASE_SUCCESS_RESPONSE,
  BASE_ERROR_RESPONSE
} = require('./base-config')

/**
 * 创建成功响应
 * @param {any} data - 响应数据
 * @param {string} message - 成功消息
 * @returns {object} 成功响应对象
 */
function createSuccessResponse(data = null, message = '操作成功') {
  return {
    ...BASE_SUCCESS_RESPONSE,
    message,
    data,
    errMsg: message // 兼容旧格式
  }
}

/**
 * 创建错误响应
 * @param {string} errCode - 错误码
 * @param {string} errMsg - 错误消息
 * @param {any} details - 错误详情
 * @returns {object} 错误响应对象
 */
function createErrorResponse(errCode = COMMON_ERROR_CODES.UNKNOWN_ERROR, errMsg = '操作失败', details = null) {
  return {
    ...BASE_ERROR_RESPONSE,
    message: errMsg,
    errCode,
    errMsg,
    details
  }
}

/**
 * 参数校验
 * @param {object} params - 参数对象
 * @param {Array} requiredFields - 必需字段数组
 * @returns {object|null} 错误响应或null
 */
function validateParams(params, requiredFields) {
  for (const field of requiredFields) {
    if (params[field] === undefined || params[field] === null || params[field] === '') {
      return createErrorResponse(
        COMMON_ERROR_CODES.PARAM_IS_NULL,
        `参数 ${field} 不能为空`
      )
    }
  }
  return null
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 格式化日期时间
 * @param {Date} date - 日期对象
 * @returns {string} ISO格式的日期时间字符串
 */
function formatDateTime(date = new Date()) {
  return date.toISOString()
}

/**
 * 格式化日期
 * @param {Date} date - 日期对象
 * @returns {string} YYYY-MM-DD格式的日期字符串
 */
function formatDate(date = new Date()) {
  return date.toISOString().split('T')[0]
}

/**
 * 安全的JSON解析
 * @param {string} jsonString - JSON字符串
 * @param {any} defaultValue - 默认值
 * @returns {any} 解析结果或默认值
 */
function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.warn('[safeJsonParse] JSON解析失败:', error.message)
    return defaultValue
  }
}

/**
 * 移除对象中的空值字段
 * @param {object} obj - 源对象
 * @returns {object} 清理后的对象
 */
function removeEmptyFields(obj) {
  const cleaned = {}
  for (const [key, value] of Object.entries(obj)) {
    if (value !== null && value !== undefined && value !== '') {
      cleaned[key] = value
    }
  }
  return cleaned
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  generateId,
  formatDateTime,
  formatDate,
  safeJsonParse,
  removeEmptyFields
}
```

#### 4. 工具基类 (common/base-tool.js)
```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/common/base-tool.js
const { COMMON_ERROR_CODES } = require('./base-config')
const { createErrorResponse } = require('./base-utils')

/**
 * 工具基类
 * 提供通用的工具执行框架和错误处理
 */
class BaseTool {
  constructor(toolName) {
    this.toolName = toolName
    this.db = uniCloud.database()
    console.log(`[${this.toolName}] 工具初始化完成`)
  }

  /**
   * 统一的工具执行入口
   * 子类需要实现 getAvailableMethods() 和具体的方法
   * @param {string} method - 方法名
   * @param {object} parameters - 参数对象
   * @returns {object} 执行结果
   */
  async execute(method, parameters = {}) {
    const executeStartTime = Date.now()

    console.log(`[${this.toolName}] [execute] 方法开始：${method}`)
    console.log(`[${this.toolName}] [execute] 输入参数`, { method, parameters })

    try {
      // 检查方法是否存在
      const availableMethods = this.getAvailableMethods()
      if (!availableMethods.includes(method)) {
        const error = new Error(`未知的方法：${method}`)
        console.error(`[${this.toolName}] [execute] 未知方法`, {
          method,
          availableMethods,
          error: error.message
        })
        throw error
      }

      // 执行具体方法
      const result = await this[method](parameters)

      const executeEndTime = Date.now()
      console.log(`[${this.toolName}] [execute] 方法执行成功：${method}`, {
        duration: executeEndTime - executeStartTime,
        resultStatus: result?.errCode === null || result?.errCode === 0 ? 'success' : 'error'
      })

      return result

    } catch (error) {
      const executeEndTime = Date.now()
      console.error(`[${this.toolName}] [execute] 方法执行异常：${method}`, {
        error: error.message,
        duration: executeEndTime - executeStartTime
      })

      return createErrorResponse(COMMON_ERROR_CODES.UNKNOWN_ERROR, error.message, error)
    }
  }

  /**
   * 获取可用方法列表
   * 子类必须实现此方法
   * @returns {Array} 方法名数组
   */
  getAvailableMethods() {
    throw new Error('子类必须实现 getAvailableMethods 方法')
  }
}

module.exports = BaseTool
```

#### 5. 记忆特有配置 (memory/config.js)
```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/config.js
const { COMMON_ERROR_CODES, BASE_CONFIG } = require('../common/base-config')

// 记忆模块特有配置
const MEMORY_CONFIG = {
  // 继承基础配置
  ...BASE_CONFIG,

  // 记忆特有配置
  MAX_MEMORIES_PER_USER: 100,
  MEMORY_QUERY_LIMIT: 50,

  // 记忆识别关键词
  MEMORY_KEYWORDS: [
    '请记住', '记住', '记录一下', '保存这个信息',
    '我要告诉你', '你需要知道', '重要信息'
  ]
}

// 记忆模块特有错误码
const MEMORY_ERROR_CODES = {
  // 继承通用错误码
  ...COMMON_ERROR_CODES,

  // 记忆特有错误码
  MEMORY_NOT_FOUND: 'MEMORY_NOT_FOUND',
  CONTENT_TOO_LONG: 'CONTENT_TOO_LONG',
  MEMORY_LIMIT_EXCEEDED: 'MEMORY_LIMIT_EXCEEDED'
}

module.exports = {
  MEMORY_CONFIG,
  MEMORY_ERROR_CODES
}
```

#### 6. 记忆管理核心类 (memory/memories.js)
```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/memories.js
const { MEMORY_CONFIG, MEMORY_ERROR_CODES } = require('./config')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  generateId,
  formatDateTime
} = require('../common/base-utils')

/**
 * 记忆管理类
 */
class MemoryManager {
  constructor() {
    this.db = uniCloud.database()
    this.collection = this.db.collection('memory')
  }

  /**
   * 创建记忆
   */
  async createMemory(options = {}) {
    const { userId, content, startDate = null, endDate = null } = options

    // 参数校验
    const validation = validateParams({ userId, content }, ['userId', 'content'])
    if (validation) return validation

    // 内容长度校验
    if (content.length > MEMORY_CONFIG.MAX_CONTENT_LENGTH) {
      return createErrorResponse(
        MEMORY_ERROR_CODES.CONTENT_TOO_LONG,
        `记忆内容不能超过${MEMORY_CONFIG.MAX_CONTENT_LENGTH}个字符`
      )
    }

    try {
      // 检查用户记忆数量限制
      const { total } = await this.collection.where({ userId }).count()
      if (total >= MEMORY_CONFIG.MAX_MEMORIES_PER_USER) {
        return createErrorResponse(
          MEMORY_ERROR_CODES.MEMORY_LIMIT_EXCEEDED,
          `每个用户最多只能保存${MEMORY_CONFIG.MAX_MEMORIES_PER_USER}条记忆`
        )
      }

      const now = formatDateTime()
      const memoryData = {
        _id: generateId(),
        userId,
        content: content.trim(),
        startDate,
        endDate,
        createTime: now,
        updateTime: now
      }

      const result = await this.collection.add(memoryData)

      return createSuccessResponse({
        memoryId: result.id,
        ...memoryData
      }, '记忆创建成功')

    } catch (error) {
      console.error('[MemoryManager.createMemory] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }

  // 其他方法类似，使用共享的工具函数...
}

module.exports = MemoryManager
```

#### 7. 记忆工具主入口 (memory/index.js)
```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/index.js
const BaseTool = require('../common/base-tool')
const MemoryManager = require('./memories')

/**
 * 记忆工具类
 * 继承BaseTool，实现记忆相关功能
 */
class MemoryTool extends BaseTool {
  constructor() {
    super('MemoryTool')
    this.memoryManager = new MemoryManager()
  }

  /**
   * 获取可用方法列表
   * @returns {Array} 方法名数组
   */
  getAvailableMethods() {
    return ['createMemory', 'getMemories', 'updateMemory', 'deleteMemory']
  }

  /**
   * 创建记忆
   */
  async createMemory(parameters) {
    return await this.memoryManager.createMemory(parameters)
  }

  /**
   * 获取记忆列表
   */
  async getMemories(parameters) {
    return await this.memoryManager.getMemories(parameters)
  }

  /**
   * 更新记忆
   */
  async updateMemory(parameters) {
    return await this.memoryManager.updateMemory(parameters)
  }

  /**
   * 删除记忆
   */
  async deleteMemory(parameters) {
    return await this.memoryManager.deleteMemory(parameters)
  }
}

module.exports = MemoryTool
```

#### 8. Todo工具重构示例 (todo/index.js)
```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/todo/index.js
const BaseTool = require('../common/base-tool')
const AuthManager = require('./auth')
const TaskManager = require('./tasks')
const ProjectManager = require('./projects')
const TagManager = require('./tags')

/**
 * Todo工具类
 * 继承BaseTool，重构现有功能
 */
class TodoTool extends BaseTool {
  constructor() {
    super('TodoTool')

    // 初始化各个管理器
    this.authManager = new AuthManager()
    this.taskManager = new TaskManager(this.authManager)
    this.projectManager = new ProjectManager(this.authManager)
    this.tagManager = new TagManager(this.authManager)

    // 其他现有配置保持不变...
  }

  /**
   * 获取可用方法列表
   */
  getAvailableMethods() {
    return [
      'login', 'initWithToken', 'getBatchData',
      'getTasks', 'createTask', 'updateTask', 'deleteTask', 'getTask',
      'getProjects', 'createProject', 'updateProject', 'deleteProject', 'getProject',
      'getTags', 'createTag', 'updateTag', 'deleteTag', 'renameTag', 'mergeTags',
      'getCurrentTimeInfo'
    ]
  }

  // 现有方法保持不变，只是去掉了重复的execute逻辑...
  async getTasks(parameters) {
    await this.authManager.ensureAuthenticated()
    return await this.taskManager.getTasks(parameters)
  }

  async createTask(parameters) {
    await this.authManager.ensureAuthenticated()
    return await this.taskManager.createTask(parameters)
  }

  // ... 其他方法
}

module.exports = TodoTool
```

#### 5. AI工具注册和集成
```javascript
// uniCloud-aliyun/cloudfunctions/ai/index.obj.js 修改部分

// 导入记忆工具
const MemoryTool = require('./modules/memory')

module.exports = {
  // 现有代码...

  /**
   * 直接调用工具（扩展支持记忆工具）
   */
  async callToolDirect({ toolName, parameters = {}, token = null }) {
    if (!toolName) {
      return { errCode: 'PARAM_IS_NULL', errMsg: 'toolName 不能为空' }
    }

    try {
      console.log('[callToolDirect] start', { toolName, parameters })

      // 记忆工具路由
      if (toolName.startsWith('memory_') || ['createMemory', 'getMemories', 'updateMemory', 'deleteMemory'].includes(toolName)) {
        const memoryTool = new MemoryTool()
        const method = toolName.startsWith('memory_') ? toolName.replace('memory_', '') : toolName

        // 自动添加用户ID
        if (!parameters.userId) {
          const token = this.getUniIdToken()
          if (token && token.uid) {
            parameters.userId = token.uid
          } else {
            return { errCode: 'UNAUTHORIZED', errMsg: '用户未登录' }
          }
        }

        const res = await memoryTool.execute(method, parameters)
        console.log('[callToolDirect] memory tool result:', JSON.stringify(res).slice(0, 1200))
        return res
      }

      // 原有的todo工具处理
      const todoTool = new TodoTool()
      // ... 原有逻辑

    } catch (e) {
      console.error('[callToolDirect] error', e && e.message)
      return { errCode: 'SYSTEM_ERROR', errMsg: e?.message || 'callToolDirect 失败', data: null }
    }
  }
}
```

### 应用场景实现

#### 场景1：用户主动添加记忆
```
用户输入：请记住，我最近的目标是提升编程技能，特别是Vue.js和TypeScript
AI识别：这是一个记忆指令
AI操作：调用 createMemory 工具保存记忆
AI回复：好的，我已经记住了您的目标。
```

#### 场景2：AI在对话中应用记忆
```
用户输入：帮我创建一个学习任务
AI处理：
1. 调用 getMemories 工具加载用户记忆
2. 发现记忆："我最近的目标是提升编程技能，特别是Vue.js和TypeScript"
3. 将记忆加入系统提示词
4. 基于记忆提供个性化建议

AI回复：根据您提升编程技能的目标，我建议创建一个Vue.js学习任务。您想学习Vue.js的哪个方面呢？比如组合式API、状态管理或者TypeScript集成？
```

#### 场景3：记忆管理
```
用户输入：我想看看你记住了什么
AI操作：调用 getMemories 工具查询用户所有记忆
AI回复：我记住了以下内容：
1. 您最近的目标是提升编程技能，特别是Vue.js和TypeScript
2. 您喜欢在周末进行学习
3. 您偏好使用番茄工作法

用户输入：删除第2条记忆
AI操作：调用 deleteMemory 工具删除指定记忆
AI回复：已删除关于周末学习偏好的记忆。
```

## 技术实现细节

### 1. 记忆工具函数 (utils.js)
```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/utils.js
const { MEMORY_CONFIG, SUCCESS_RESPONSE, ERROR_RESPONSE } = require('./config')

/**
 * 创建成功响应
 */
function createSuccessResponse(data, message = '操作成功') {
  return {
    ...SUCCESS_RESPONSE,
    errCode: null,
    errMsg: message,
    data
  }
}

/**
 * 创建错误响应
 */
function createErrorResponse(errCode, errMsg, details = null) {
  return {
    ...ERROR_RESPONSE,
    errCode,
    errMsg,
    details
  }
}

/**
 * 参数校验
 */
function validateParams(params, requiredFields) {
  for (const field of requiredFields) {
    if (!params[field]) {
      return createErrorResponse('PARAM_IS_NULL', `参数 ${field} 不能为空`)
    }
  }
  return null
}

/**
 * 生成唯一ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 格式化日期时间
 */
function formatDateTime(date = new Date()) {
  return date.toISOString()
}

/**
 * 记忆识别逻辑
 */
function isMemoryCommand(message) {
  return MEMORY_CONFIG.MEMORY_KEYWORDS.some(keyword =>
    message.toLowerCase().includes(keyword.toLowerCase())
  )
}

/**
 * 提取记忆内容
 */
function extractMemoryContent(message) {
  const patterns = [
    /请记住[，：:]\s*(.*)/,
    /记住[，：:]\s*(.*)/,
    /记录一下[，：:]\s*(.*)/,
    /保存这个信息[，：:]\s*(.*)/,
    /我要告诉你[，：:]\s*(.*)/,
    /你需要知道[，：:]\s*(.*)/
  ]

  for (const pattern of patterns) {
    const match = message.match(pattern)
    if (match) {
      return match[1].trim()
    }
  }

  return message.trim()
}

/**
 * 构建包含记忆的系统提示词
 */
function buildSystemPromptWithMemories(memories) {
  if (!memories || memories.length === 0) {
    return '你是一个AI助手，帮助用户管理任务。'
  }

  const memoryText = memories
    .map((m, index) => `${index + 1}. ${m.content}`)
    .join('\n')

  return `你是一个AI助手，帮助用户管理任务。

用户之前告诉过你以下信息，请在回答时考虑这些背景：
${memoryText}

请基于这些信息提供个性化的建议和回复。`
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  generateId,
  formatDateTime,
  isMemoryCommand,
  extractMemoryContent,
  buildSystemPromptWithMemories
}
```

### 2. chatStreamSSE集成记忆功能
```javascript
// uniCloud-aliyun/cloudfunctions/ai/index.obj.js 中的 chatStreamSSE 修改

async chatStreamSSE({ message, messages, channel }) {
  const userId = this.getUniIdToken()?.uid

  try {
    // 导入记忆工具函数
    const { isMemoryCommand, extractMemoryContent, buildSystemPromptWithMemories } =
      require('./modules/memory/utils')
    const MemoryTool = require('./modules/memory')

    // 1. 检查是否是记忆指令
    if (isMemoryCommand(message)) {
      const memoryContent = extractMemoryContent(message)
      const memoryTool = new MemoryTool()

      // 创建记忆
      const result = await memoryTool.execute('createMemory', {
        userId,
        content: memoryContent
      })

      if (result.errCode) {
        channel.write({
          type: 'message',
          data: { content: `记忆保存失败：${result.errMsg}` }
        })
      } else {
        channel.write({
          type: 'message',
          data: { content: '好的，我已经记住了这个信息。' }
        })
      }

      return { errCode: 0, errMsg: 'success' }
    }

    // 2. 加载用户记忆并构建系统提示词
    let systemPrompt = '你是一个AI助手，帮助用户管理任务。'

    if (userId) {
      const memoryTool = new MemoryTool()
      const memoriesResult = await memoryTool.execute('getMemories', {
        userId,
        limit: 10
      })

      if (!memoriesResult.errCode && memoriesResult.data) {
        systemPrompt = buildSystemPromptWithMemories(memoriesResult.data)
      }
    }

    // 3. 使用包含记忆的系统提示词进行AI对话
    // ... 原有的AI对话逻辑，使用修改后的systemPrompt

  } catch (error) {
    console.error('记忆功能错误:', error)
    // 继续正常的AI对话流程
  }
}
```

### 3. 工具注册到AI系统
```javascript
// 在AI系统中注册记忆工具（参考现有todo工具的注册方式）

// 可以通过以下方式调用记忆工具：
// 1. 直接工具调用
await this.callToolDirect({
  toolName: 'createMemory',
  parameters: {
    content: '用户的记忆内容',
    startDate: '2024-01-01',
    endDate: '2024-12-31'
  }
})

// 2. 批量操作
await this.callToolDirect({
  toolName: 'getMemories',
  parameters: {
    limit: 20,
    offset: 0
  }
})

// 3. 记忆管理
await this.callToolDirect({
  toolName: 'updateMemory',
  parameters: {
    memoryId: 'memory_123',
    content: '更新后的记忆内容'
  }
})

await this.callToolDirect({
  toolName: 'deleteMemory',
  parameters: {
    memoryId: 'memory_123'
  }
})
```

## 重构优势

### 1. **代码复用性**
- ✅ 消除了重复的响应模板和错误码定义
- ✅ 通用工具函数可被多个模块共享
- ✅ 统一的工具执行框架，减少样板代码

### 2. **维护性提升**
- ✅ 修改通用逻辑只需在一个地方进行
- ✅ 新增工具模块时可快速继承基础能力
- ✅ 错误处理和日志记录标准化

### 3. **扩展性增强**
- ✅ 基于继承的设计，便于添加新的工具模块
- ✅ 配置分层，各模块只关注自己特有的配置
- ✅ 统一的接口规范，便于工具间协作

### 4. **一致性保证**
- ✅ 所有工具模块使用相同的响应格式
- ✅ 统一的错误处理和参数校验逻辑
- ✅ 标准化的日志输出格式

## 实施计划

### 第一阶段：基础架构重构 (2天)
1. **创建共享模块**
   - 创建 `modules/common/` 文件夹
   - 实现 `base-config.js`、`base-utils.js`、`base-tool.js`
   - 从现有todo模块中提取通用代码

2. **Todo模块重构**
   - 修改 `todo/config.js` 只保留todo特有配置
   - 修改 `todo/utils.js` 只保留todo特有工具函数
   - 修改 `todo/index.js` 继承BaseTool

3. **向后兼容性测试**
   - 确保现有todo功能正常工作
   - 验证API响应格式兼容性

### 第二阶段：记忆模块开发 (2天)
1. **数据库Schema创建**
   - 创建 `uniCloud-aliyun/database/memory.schema.json`
   - 配置权限和字段验证规则

2. **记忆工具模块开发**
   - 创建 `modules/memory/` 文件夹结构
   - 实现继承BaseTool的MemoryTool
   - 实现记忆管理核心逻辑

3. **AI系统集成**
   - 在 `ai/index.obj.js` 中集成MemoryTool
   - 实现chatStreamSSE记忆功能集成

### 第三阶段：测试和优化 (2天)
1. **功能测试**
   - 记忆CRUD操作测试
   - AI对话记忆应用测试
   - 工具间协作测试

2. **前端界面开发**
   - 记忆管理界面
   - API调用封装

3. **性能优化和部署**
   - 查询性能优化
   - 生产环境部署

## 成功指标

### 技术指标
- 记忆存储成功率 > 99%
- 记忆查询响应时间 < 200ms
- 系统稳定性 > 99.5%
- 工具调用成功率 > 95%

### 功能指标
- 记忆识别准确率 > 90%
- AI对话个性化程度提升
- 记忆管理操作便捷性
- 用户记忆数据安全性

### 用户体验指标
- 记忆功能使用率 > 60%
- 用户满意度提升
- 记忆功能学习成本低
- 界面操作直观易用
