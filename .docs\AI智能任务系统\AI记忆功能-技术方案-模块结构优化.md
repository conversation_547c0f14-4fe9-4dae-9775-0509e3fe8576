# AI智能任务系统记忆功能 - 技术方案 - 模块结构优化

## 文档说明
本文档专门描述基础配置抽离、工具函数重构、工具基类设计等模块结构优化相关内容。这是后续开发的基础，需要优先实施。

**前置条件：** 需要先完成需求文档的评审和确认。

**依赖关系：** 本文档是记忆工具开发和其他功能实现的基础，必须优先完成。

## 优化目标

### 1. 代码复用性
- ✅ 消除重复的响应模板和错误码定义
- ✅ 通用工具函数可被多个模块共享
- ✅ 统一的工具执行框架，减少样板代码

### 2. 维护性提升
- ✅ 修改通用逻辑只需在一个地方进行
- ✅ 新增工具模块时可快速继承基础能力
- ✅ 错误处理和日志记录标准化

### 3. 扩展性增强
- ✅ 基于继承的设计，便于添加新的工具模块
- ✅ 配置分层，各模块只关注自己特有的配置
- ✅ 统一的接口规范，便于工具间协作

### 4. 一致性保证
- ✅ 所有工具模块使用相同的响应格式
- ✅ 统一的错误处理和参数校验逻辑
- ✅ 标准化的日志输出格式

## 优化后的模块结构

```
uniCloud-aliyun/cloudfunctions/ai/modules/
├── common/                    # 新增：共享工具模块
│   ├── base-config.js        # 基础配置和错误码
│   ├── base-utils.js         # 通用工具函数
│   └── base-tool.js          # 工具基类
├── todo/                     # 现有todo模块（需要重构）
│   ├── index.js             # 继承BaseTool
│   ├── config.js            # 只保留todo特有配置
│   ├── tasks.js
│   ├── projects.js
│   └── utils.js             # 只保留todo特有工具函数
└── memory/                   # 新增记忆模块
    ├── index.js             # 继承BaseTool
    ├── config.js            # 只保留记忆特有配置
    ├── memories.js          # 记忆管理核心逻辑
    └── utils.js             # 只保留记忆特有工具函数
```

## 共享基础配置 (common/base-config.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/common/base-config.js

// 通用错误码定义
const COMMON_ERROR_CODES = {
  // 参数错误
  PARAM_IS_NULL: 'PARAM_IS_NULL',
  PARAM_INVALID: 'PARAM_INVALID',

  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',

  // 数据库错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  DATA_NOT_FOUND: 'DATA_NOT_FOUND',

  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',

  // 业务错误
  OPERATION_FAILED: 'OPERATION_FAILED',
  RESOURCE_LIMIT_EXCEEDED: 'RESOURCE_LIMIT_EXCEEDED',

  // 未知错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

// 通用响应模板
const BASE_SUCCESS_RESPONSE = {
  success: true,
  message: '操作成功',
  data: null,
  // 兼容旧格式
  errCode: null,
  errMsg: '操作成功'
}

const BASE_ERROR_RESPONSE = {
  success: false,
  message: '操作失败',
  data: null,
  // 兼容旧格式
  errCode: 'UNKNOWN_ERROR',
  errMsg: '操作失败',
  details: null
}

// 通用配置
const BASE_CONFIG = {
  // 数据库查询限制
  DEFAULT_QUERY_LIMIT: 50,
  MAX_QUERY_LIMIT: 200,

  // 日期时间格式
  DATE_FORMAT: 'YYYY-MM-DD',
  DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',

  // 通用字段长度限制
  MAX_TITLE_LENGTH: 200,
  MAX_CONTENT_LENGTH: 2000,
  MAX_DESCRIPTION_LENGTH: 500
}

module.exports = {
  COMMON_ERROR_CODES,
  BASE_SUCCESS_RESPONSE,
  BASE_ERROR_RESPONSE,
  BASE_CONFIG
}
```

## 共享工具函数 (common/base-utils.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/common/base-utils.js
const {
  COMMON_ERROR_CODES,
  BASE_SUCCESS_RESPONSE,
  BASE_ERROR_RESPONSE
} = require('./base-config')

/**
 * 创建成功响应
 * @param {any} data - 响应数据
 * @param {string} message - 成功消息
 * @returns {object} 成功响应对象
 */
function createSuccessResponse(data = null, message = '操作成功') {
  return {
    ...BASE_SUCCESS_RESPONSE,
    message,
    data,
    errMsg: message // 兼容旧格式
  }
}

/**
 * 创建错误响应
 * @param {string} errCode - 错误码
 * @param {string} errMsg - 错误消息
 * @param {any} details - 错误详情
 * @returns {object} 错误响应对象
 */
function createErrorResponse(errCode = COMMON_ERROR_CODES.UNKNOWN_ERROR, errMsg = '操作失败', details = null) {
  return {
    ...BASE_ERROR_RESPONSE,
    message: errMsg,
    errCode,
    errMsg,
    details
  }
}

/**
 * 参数校验
 * @param {object} params - 参数对象
 * @param {Array} requiredFields - 必需字段数组
 * @returns {object|null} 错误响应或null
 */
function validateParams(params, requiredFields) {
  for (const field of requiredFields) {
    if (params[field] === undefined || params[field] === null || params[field] === '') {
      return createErrorResponse(
        COMMON_ERROR_CODES.PARAM_IS_NULL,
        `参数 ${field} 不能为空`
      )
    }
  }
  return null
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 格式化日期时间
 * @param {Date} date - 日期对象
 * @returns {string} ISO格式的日期时间字符串
 */
function formatDateTime(date = new Date()) {
  return date.toISOString()
}

/**
 * 格式化日期
 * @param {Date} date - 日期对象
 * @returns {string} YYYY-MM-DD格式的日期字符串
 */
function formatDate(date = new Date()) {
  return date.toISOString().split('T')[0]
}

/**
 * 安全的JSON解析
 * @param {string} jsonString - JSON字符串
 * @param {any} defaultValue - 默认值
 * @returns {any} 解析结果或默认值
 */
function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.warn('[safeJsonParse] JSON解析失败:', error.message)
    return defaultValue
  }
}

/**
 * 移除对象中的空值字段
 * @param {object} obj - 源对象
 * @returns {object} 清理后的对象
 */
function removeEmptyFields(obj) {
  const cleaned = {}
  for (const [key, value] of Object.entries(obj)) {
    if (value !== null && value !== undefined && value !== '') {
      cleaned[key] = value
    }
  }
  return cleaned
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  generateId,
  formatDateTime,
  formatDate,
  safeJsonParse,
  removeEmptyFields
}
```

## 工具基类 (common/base-tool.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/common/base-tool.js
const { COMMON_ERROR_CODES } = require('./base-config')
const { createErrorResponse } = require('./base-utils')

/**
 * 工具基类
 * 提供通用的工具执行框架和错误处理
 */
class BaseTool {
  constructor(toolName) {
    this.toolName = toolName
    this.db = uniCloud.database()
    console.log(`[${this.toolName}] 工具初始化完成`)
  }

  /**
   * 统一的工具执行入口
   * 子类需要实现 getAvailableMethods() 和具体的方法
   * @param {string} method - 方法名
   * @param {object} parameters - 参数对象
   * @returns {object} 执行结果
   */
  async execute(method, parameters = {}) {
    const executeStartTime = Date.now()

    console.log(`[${this.toolName}] [execute] 方法开始：${method}`)
    console.log(`[${this.toolName}] [execute] 输入参数`, { method, parameters })

    try {
      // 检查方法是否存在
      const availableMethods = this.getAvailableMethods()
      if (!availableMethods.includes(method)) {
        const error = new Error(`未知的方法：${method}`)
        console.error(`[${this.toolName}] [execute] 未知方法`, {
          method,
          availableMethods,
          error: error.message
        })
        throw error
      }

      // 执行具体方法
      const result = await this[method](parameters)

      const executeEndTime = Date.now()
      console.log(`[${this.toolName}] [execute] 方法执行成功：${method}`, {
        duration: executeEndTime - executeStartTime,
        resultStatus: result?.errCode === null || result?.errCode === 0 ? 'success' : 'error'
      })

      return result

    } catch (error) {
      const executeEndTime = Date.now()
      console.error(`[${this.toolName}] [execute] 方法执行异常：${method}`, {
        error: error.message,
        duration: executeEndTime - executeStartTime
      })

      return createErrorResponse(COMMON_ERROR_CODES.UNKNOWN_ERROR, error.message, error)
    }
  }

  /**
   * 获取可用方法列表
   * 子类必须实现此方法
   * @returns {Array} 方法名数组
   */
  getAvailableMethods() {
    throw new Error('子类必须实现 getAvailableMethods 方法')
  }
}

module.exports = BaseTool
```

## Todo工具重构示例 (todo/index.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/todo/index.js
const BaseTool = require('../common/base-tool')
const AuthManager = require('./auth')
const TaskManager = require('./tasks')
const ProjectManager = require('./projects')
const TagManager = require('./tags')

/**
 * Todo工具类
 * 继承BaseTool，重构现有功能
 */
class TodoTool extends BaseTool {
  constructor() {
    super('TodoTool')

    // 初始化各个管理器
    this.authManager = new AuthManager()
    this.taskManager = new TaskManager(this.authManager)
    this.projectManager = new ProjectManager(this.authManager)
    this.tagManager = new TagManager(this.authManager)

    // 其他现有配置保持不变...
  }

  /**
   * 获取可用方法列表
   */
  getAvailableMethods() {
    return [
      'login', 'initWithToken', 'getBatchData',
      'getTasks', 'createTask', 'updateTask', 'deleteTask', 'getTask',
      'getProjects', 'createProject', 'updateProject', 'deleteProject', 'getProject',
      'getTags', 'createTag', 'updateTag', 'deleteTag', 'renameTag', 'mergeTags',
      'getCurrentTimeInfo'
    ]
  }

  // 现有方法保持不变，只是去掉了重复的execute逻辑...
  async getTasks(parameters) {
    await this.authManager.ensureAuthenticated()
    return await this.taskManager.getTasks(parameters)
  }

  async createTask(parameters) {
    await this.authManager.ensureAuthenticated()
    return await this.taskManager.createTask(parameters)
  }

  // ... 其他方法
}

module.exports = TodoTool
```

## 实施计划

### 第一阶段：基础架构重构 (2天)

1. **创建共享模块**
   - 创建 `modules/common/` 文件夹
   - 实现 `base-config.js`、`base-utils.js`、`base-tool.js`
   - 从现有todo模块中提取通用代码

2. **Todo模块重构**
   - 修改 `todo/config.js` 只保留todo特有配置
   - 修改 `todo/utils.js` 只保留todo特有工具函数
   - 修改 `todo/index.js` 继承BaseTool

3. **向后兼容性测试**
   - 确保现有todo功能正常工作
   - 验证API响应格式兼容性

### 重构验证清单

- [ ] 共享配置模块创建完成
- [ ] 共享工具函数模块创建完成
- [ ] 工具基类实现完成
- [ ] Todo模块重构完成
- [ ] 现有功能兼容性测试通过
- [ ] 代码复用率提升验证
- [ ] 错误处理统一性验证
```
